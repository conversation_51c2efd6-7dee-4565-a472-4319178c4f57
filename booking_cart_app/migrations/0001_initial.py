# Generated by Django 5.0.6 on 2025-07-05 05:42

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booking_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique booking identifier', unique=True)),
                ('friendly_id', models.CharField(blank=True, help_text='Customer-friendly booking reference (e.g., CW-2024-001234)', max_length=20, unique=True)),
                ('slug', models.SlugField(blank=True, help_text='Short reference code for URLs', max_length=8, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('declined', 'Declined'), ('completed', 'Completed'), ('disputed', 'Disputed'), ('no_show', 'No Show')], default='pending', help_text='Current status of the booking', max_length=20)),
                ('total_price', models.DecimalField(decimal_places=2, help_text='Total price for all services in this booking', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('booking_date', models.DateTimeField(auto_now_add=True, help_text='When the booking was created')),
                ('notes', models.TextField(blank=True, help_text='Additional notes from customer (max 500 characters)', max_length=500)),
                ('cancellation_reason', models.TextField(blank=True, help_text='Reason for cancellation (if applicable)', max_length=500)),
                ('dispute_reason', models.TextField(blank=True, help_text='Reason for dispute (if applicable)', max_length=500)),
                ('dispute_filed_by', models.CharField(blank=True, choices=[('customer', 'Customer'), ('provider', 'Provider'), ('admin', 'Admin')], help_text='Who filed the dispute', max_length=20)),
                ('dispute_filed_at', models.DateTimeField(blank=True, help_text='When the dispute was filed', null=True)),
                ('dispute_resolved_at', models.DateTimeField(blank=True, help_text='When the dispute was resolved', null=True)),
                ('dispute_resolution_notes', models.TextField(blank=True, help_text='Admin notes for dispute resolution', max_length=1000)),
                ('last_status_change', models.DateTimeField(auto_now=True, help_text='When the status was last changed')),
                ('customer', models.ForeignKey(help_text='Customer who made the booking', on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue where services will be provided', on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Booking',
                'verbose_name_plural': 'Bookings',
                'ordering': ['-booking_date'],
            },
        ),
        migrations.CreateModel(
            name='BookingItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_title', models.CharField(help_text='Service title at time of booking (for record keeping)', max_length=255)),
                ('service_price', models.DecimalField(decimal_places=2, help_text='Service price at time of booking', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Number of appointments for this service', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('scheduled_date', models.DateField(help_text='Date when service is scheduled')),
                ('scheduled_time', models.TimeField(help_text='Time when service is scheduled')),
                ('duration_minutes', models.PositiveIntegerField(help_text='Duration of service in minutes (copied from service)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this booking item was created')),
                ('booking', models.ForeignKey(help_text='Booking this item belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='items', to='booking_cart_app.booking')),
                ('service', models.ForeignKey(help_text='Service being booked', on_delete=django.db.models.deletion.CASCADE, related_name='booking_items', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Booking Item',
                'verbose_name_plural': 'Booking Items',
                'ordering': ['scheduled_date', 'scheduled_time'],
            },
        ),
        migrations.CreateModel(
            name='BookingStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(max_length=20)),
                ('new_status', models.CharField(max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='booking_cart_app.booking')),
            ],
            options={
                'verbose_name': 'Booking Status History',
                'verbose_name_plural': 'Booking Status Histories',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the cart was created')),
                ('expires_at', models.DateTimeField(help_text='When the cart expires (24 hours after creation)')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the cart was last updated')),
                ('customer', models.OneToOneField(help_text='Customer who owns this cart', on_delete=django.db.models.deletion.CASCADE, related_name='cart', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Cart',
                'verbose_name_plural': 'Carts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selected_date', models.DateField(help_text='Date for the service appointment')),
                ('selected_time_slot', models.TimeField(help_text='Time slot for the service appointment')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Number of appointments (max 10)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('price_per_item', models.DecimalField(decimal_places=2, help_text='Price per service including any applicable discounts', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('added_at', models.DateTimeField(auto_now_add=True, help_text='When the item was added to cart')),
                ('cart', models.ForeignKey(help_text='Cart this item belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='items', to='booking_cart_app.cart')),
                ('service', models.ForeignKey(help_text='Service being booked', on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Cart Item',
                'verbose_name_plural': 'Cart Items',
                'ordering': ['selected_date', 'selected_time_slot'],
            },
        ),
        migrations.CreateModel(
            name='RecurringAvailabilityPattern',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this recurring pattern', max_length=100)),
                ('pattern_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], help_text='Type of recurring pattern', max_length=20)),
                ('start_time', models.TimeField(help_text='Start time for availability slots')),
                ('end_time', models.TimeField(help_text='End time for availability slots')),
                ('slot_duration_minutes', models.PositiveIntegerField(default=60, help_text='Duration of each slot in minutes', validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(480)])),
                ('break_between_slots', models.PositiveIntegerField(default=0, help_text='Break time between slots in minutes', validators=[django.core.validators.MaxValueValidator(120)])),
                ('monday', models.BooleanField(default=True, verbose_name='Monday')),
                ('tuesday', models.BooleanField(default=True, verbose_name='Tuesday')),
                ('wednesday', models.BooleanField(default=True, verbose_name='Wednesday')),
                ('thursday', models.BooleanField(default=True, verbose_name='Thursday')),
                ('friday', models.BooleanField(default=True, verbose_name='Friday')),
                ('saturday', models.BooleanField(default=False, verbose_name='Saturday')),
                ('sunday', models.BooleanField(default=False, verbose_name='Sunday')),
                ('start_date', models.DateField(help_text='Start date for pattern application')),
                ('end_date', models.DateField(blank=True, help_text='End date for pattern application (leave blank for ongoing)', null=True)),
                ('max_bookings_per_slot', models.PositiveIntegerField(default=1, help_text='Maximum bookings per generated slot', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(20)])),
                ('is_active', models.BooleanField(default=True, help_text='Whether this pattern is actively generating slots')),
                ('generate_advance_days', models.PositiveIntegerField(default=30, help_text='How many days in advance to generate slots', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)])),
                ('exclude_holidays', models.BooleanField(default=False, help_text='Whether to exclude holidays from generation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_generated', models.DateTimeField(blank=True, help_text='When slots were last generated for this pattern', null=True)),
                ('service', models.ForeignKey(help_text='Service this pattern applies to', on_delete=django.db.models.deletion.CASCADE, related_name='recurring_patterns', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Recurring Availability Pattern',
                'verbose_name_plural': 'Recurring Availability Patterns',
                'ordering': ['service', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PatternDateExclusion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excluded_date', models.DateField(help_text='Date to exclude from pattern generation')),
                ('reason', models.CharField(blank=True, help_text='Reason for excluding this date', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pattern', models.ForeignKey(help_text='Pattern to exclude date from', on_delete=django.db.models.deletion.CASCADE, related_name='date_exclusions', to='booking_cart_app.recurringavailabilitypattern')),
            ],
            options={
                'verbose_name': 'Pattern Date Exclusion',
                'verbose_name_plural': 'Pattern Date Exclusions',
                'ordering': ['excluded_date'],
            },
        ),
        migrations.CreateModel(
            name='ServiceAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('available_date', models.DateField(db_index=True, help_text='Date when service is available')),
                ('start_time', models.TimeField(help_text='Start time of availability slot')),
                ('end_time', models.TimeField(help_text='End time of availability slot')),
                ('max_bookings', models.PositiveIntegerField(default=1, help_text='Maximum number of bookings for this slot', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(20)])),
                ('current_bookings', models.PositiveIntegerField(default=0, help_text='Current number of confirmed bookings for this slot')),
                ('is_available', models.BooleanField(default=True, help_text='Whether this slot is available for booking')),
                ('is_recurring', models.BooleanField(default=False, help_text='Whether this slot was created from a recurring pattern')),
                ('is_exception', models.BooleanField(default=False, help_text='Whether this slot is an exception to the normal schedule')),
                ('exception_reason', models.CharField(blank=True, help_text='Reason for the schedule exception', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this availability slot was created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When this availability slot was last updated')),
                ('recurring_pattern', models.ForeignKey(blank=True, help_text='Recurring pattern that generated this slot', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_slots', to='booking_cart_app.recurringavailabilitypattern')),
                ('service', models.ForeignKey(help_text='Service this availability is for', on_delete=django.db.models.deletion.CASCADE, related_name='availability_slots', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Service Availability',
                'verbose_name_plural': 'Service Availabilities',
                'ordering': ['available_date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='ServiceAvailabilityTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this availability template', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of this template')),
                ('configuration', models.JSONField(help_text='JSON configuration for availability settings')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default template for the service')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('service', models.ForeignKey(help_text='Service this template applies to', on_delete=django.db.models.deletion.CASCADE, related_name='availability_templates', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Service Availability Template',
                'verbose_name_plural': 'Service Availability Templates',
                'ordering': ['service', 'name'],
            },
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['status'], name='booking_car_status_545363_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['booking_date'], name='booking_car_booking_fbece0_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['friendly_id'], name='booking_car_friendl_687387_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='bookingitem',
            unique_together={('booking', 'service', 'scheduled_date', 'scheduled_time')},
        ),
        migrations.AlterUniqueTogether(
            name='cartitem',
            unique_together={('cart', 'service', 'selected_date', 'selected_time_slot')},
        ),
        migrations.AlterUniqueTogether(
            name='patterndateexclusion',
            unique_together={('pattern', 'excluded_date')},
        ),
        migrations.AddIndex(
            model_name='serviceavailability',
            index=models.Index(fields=['available_date'], name='booking_car_availab_b1cb90_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceavailability',
            index=models.Index(fields=['is_recurring'], name='booking_car_is_recu_0625cd_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceavailability',
            index=models.Index(fields=['recurring_pattern'], name='booking_car_recurri_96e514_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='serviceavailability',
            unique_together={('service', 'available_date', 'start_time')},
        ),
        migrations.AlterUniqueTogether(
            name='serviceavailabilitytemplate',
            unique_together={('service', 'name')},
        ),
    ]
